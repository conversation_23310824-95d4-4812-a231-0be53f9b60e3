<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="81e64c63-5dbe-4a88-a045-4c8c345e7802" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ts.external.directory.path": "/Users/<USER>/augment/devops-ai/comate-stack-fe/node_modules/typescript/lib"
  }
}]]></component>
  <component name="TaskManager">
    <servers />
  </component>
</project>