import {StageStep} from './stage';
import {ChatStatus} from './status';

export interface ChatAnchor {
    [key: string]: string | number;
    stageId?: string;
    stepId?: string;
    tabId?: string;
    taskId?: string;
}

export type ChatActionType =
    | 'openWindow'
    | 'jumpPlatform'
    | 'closeNotification';

export interface ChatAction {
    text: string;
    buttonType?: 'primary' | 'outlined' | 'default' | 'flat' | 'text' | 'underline' | 'link' | 'dashed';
    query?: string; // 如果有，点击按钮时，会发一条消息
    callback?: string; // 回调场景
    href?: string; // 跳转链接
    anchor?: ChatAnchor; // 锚点场景
    enableConfetti?: boolean; // 是否开启烟花效果
    right?: boolean; // 是否靠右对齐
    actionTypes?: ChatActionType[];
}

export interface StepItem {
    text: string;
    status: ChatStatus;
    anchor?: ChatAnchor;
    stepId: string;
}

export interface FileContextElement {
    type: 'fileContext';
    status?: ChatStatus;
    tag?: string;
    text: string;
    extra?: string;
    description?: string;
}

export interface TextElement {
    type: 'text';
    content: string;
    renderHtml?: boolean;
}

export interface StepElement {
    type: 'steps';
    steps: StepItem[];
}

export interface StageStepElement {
    type: 'stageSteps';
    stageSteps: StageStep[];
}

export interface FormElement {
    type: 'form';
    template: string;
}

export interface ActionElement {
    type: 'actions';
    actions: ChatAction[];
}

export interface FieldElement {
    type: 'field';
    label: string;
    width?: number;
    elements: ToolElement[];
}

export interface CardElement {
    type: 'card';
    title: string;
    extra?: string;
    extraUser?: string;
    elements: ToolElement[];
}

export interface ImageElement {
    type: 'image';
    url: string;
}

export interface SmartTextElement {
    type: 'smartLink' | 'smartText';
    content: string;
}

export interface IframeElement {
    type: 'iframe';
    iframe: string;
    height?: number;
    width?: number;
}

export interface InfoflowCardElement {
    type: 'infoflowCardWebUrl';
    content: string;
}

export interface MCPToolElement {
    type: 'toolCall';
    tool: {
        type: 'mcpTool';
        serverName: string;
        toolName: string;
    };
    input: string;
    status: 'success' | 'pending' | 'fail' | 'running';
    output: Array<{
        mimeType: string;
        content: string;
    }>;
}

export type ToolElement =
    | ActionElement
    | FileContextElement
    | TextElement
    | StepElement
    | StageStepElement
    | FieldElement
    | SmartTextElement
    | CardElement
    | ImageElement
    | IframeElement
    | FormElement
    | InfoflowCardElement
    | MCPToolElement;
