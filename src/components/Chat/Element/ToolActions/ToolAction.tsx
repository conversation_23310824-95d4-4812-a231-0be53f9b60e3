import {Button} from '@panda-design/components';
import {debounce} from 'lodash';
import {useMemo} from 'react';
import {Link} from '@/links/createLink';
import {ChatAction} from '@/types/staff/element';
import {useConversationId} from '@/components/Chat/Provider/ConversationIdProvider';
import {useMessageContext} from '@/components/Chat/Provider/MessageProvider';
import {useStaffTypeIsChat} from '@/components/Chat/Provider/StaffTypeProvider';
import {handleActionClick} from './handleActionClick';
import {getButtonVariantProps} from './utils';

interface ActionProps {
    action: ChatAction;
}

export const ToolAction = ({action}: ActionProps) => {
    const {
        text,
        buttonType,
        href,
    } = action;

    const conversationId = useConversationId();
    const messageContext = useMessageContext();
    const staffTypeIsChat = useStaffTypeIsChat();

    const handleClick = useMemo(
        () =>
            debounce(async () => {
                const displayType = staffTypeIsChat
                    ? 'platform'
                    : messageContext.isNotification
                        ? 'notification'
                        : 'window';
                // 这里这样封装一下 iPipe 里面比较好抄
                handleActionClick({
                    context: {
                        displayType,
                        conversationId,
                        messageId: messageContext.messageId,
                        agentId: messageContext.agentId,
                        handleCloseNotification:
                            messageContext.handleCloseNotification,
                    },
                    action,
                });
            }, 300),
        [action, conversationId, messageContext, staffTypeIsChat]
    );

    const variantProps = useMemo(
        () => getButtonVariantProps(buttonType),
        [buttonType]
    );

    const element = (
        <Button {...variantProps} onClick={handleClick}>
            {text}
        </Button>
    );
    if (href) {
        return (
            <Link blank to={href}>
                {element}
            </Link>
        );
    }
    return element;
};
