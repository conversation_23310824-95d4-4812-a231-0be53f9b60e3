import {useMemo} from 'react';
import {useCurrentAgentId} from '@/regions/staff/agent';
import ChatMessageInput from '../ChatMessageInput';
import {useSendWelcomeMessage} from './useSendWelcomeMessage';

export const WelcomeMessageInput = () => {
    const currentAgentId = useCurrentAgentId();
    const sendWelcomeMessage = useSendWelcomeMessage();

    const [disabled, disabledReason] = useMemo(
        () => {
            let disabled = false;
            let disabledReason = '';
            if (currentAgentId === 4) {
                disabled = true;
                disabledReason = '智测师暂未开放对话功能';
            }
            if (currentAgentId === 9) {
                disabled = true;
                disabledReason = '我还不能在这里接受任务委托，去如流上搜索『前端君』就能找到我啦';
            }
            return [disabled, disabledReason];
        },
        [currentAgentId]
    );

    return (
        <ChatMessageInput
            disabled={disabled}
            disabledReason={disabledReason}
            onSend={sendWelcomeMessage}
        />
    );
};
