import constate from 'constate';
import {useCallback, useState} from 'react';
import {ChatAction} from '@/types/staff/element';

type StageHandler = (action: ChatAction) => void | Promise<void>;

interface StageHandlers {
    [taskId: string]: StageHandler;
}

interface InitType {
    initialHandlers?: StageHandlers;
    executeInPlatformAction?: (action: ChatAction) => Promise<void>;
}

const useStageContextRaw = (initialValue: InitType = {}) => {
    const [stageHandlers, setStageHandlers] = useState<StageHandlers>(
        initialValue.initialHandlers || {}
    );

    const hasInPlatformHandler = !!initialValue?.executeInPlatformAction;
    const providedExecuteInPlatformAction = initialValue?.executeInPlatformAction || (async () => {});

    const registerStageHandler = useCallback(
        (taskId: string, handler: StageHandler) => {
            setStageHandlers(prev => ({
                ...prev,
                [taskId]: handler,
            }));
        },
        []
    );

    const unregisterStageHandler = useCallback(
        (taskId: string) => {
            setStageHandlers(prev => {
                const newHandlers = {...prev};
                delete newHandlers[taskId];
                return newHandlers;
            });
        },
        []
    );

    const getStageHandler = useCallback(
        (taskId: string): StageHandler | undefined => {
            return stageHandlers[taskId];
        },
        [stageHandlers]
    );

    const executeInPlatformAction = useCallback(
        async (action: ChatAction) => {
            if (hasInPlatformHandler) {
                await providedExecuteInPlatformAction(action);
                return;
            }

            const {anchor} = action;
            if (!anchor?.taskId) {
                console.warn('inPlatform action requires taskId in anchor');
                return;
            }

            const handler = stageHandlers[anchor.taskId];
            if (!handler) {
                console.warn(`No stage handler registered for taskId: ${anchor.taskId}`);
                return;
            }

            try {
                await handler(action);
            } catch (error) {
                console.error('Error executing inPlatform action:', error);
            }
        },
        [hasInPlatformHandler, providedExecuteInPlatformAction, stageHandlers]
    );

    return {
        stageHandlers,
        registerStageHandler,
        unregisterStageHandler,
        getStageHandler,
        executeInPlatformAction,
        hasInPlatformHandler,
    };
};

export const [
    StageProvider,
    useStageContext,
] = constate(
    useStageContextRaw
);
