import {apiGetCheckIDEAResource} from '@/api/icode/webIDE';
import {CodeSpaceKindConf} from './types';

export const getCodeSpaceKind = async (): Promise<CodeSpaceKindConf[]> => {
    const hasCreatedIdeaU = await apiGetCheckIDEAResource();
    return [
        {
            codeSpaceKind: 'aiIDE',
            displayName: 'Comate AI IDE',
            description: '支持 C/C++、Go、JavaScript、PHP、Python 等多种语言',
            tags: ['c', 'c++', 'go', 'js', 'php', 'python'],
        },
        {
            codeSpaceKind: 'ideaCommunity',
            displayName: 'IDEA社区版',
            description: '友好支持 Java 开发',
            tags: ['java', 'jdk8/11', 'gradle', 'maven'],
        },
        {
            codeSpaceKind: 'pycharm',
            displayName: 'PyCharm',
            description: '友好支持 Python 开发',
            tags: ['python'],
        },
        {
            codeSpaceKind: 'swan',
            displayName: 'Swan',
            description: '支持小程序开发',
            tags: ['swan'],
        },
        {
            codeSpaceKind: 'ideaUltimate',
            displayName: 'IDEA专业版',
            description: '带正版企业 License',
            tags: ['java', 'jdk8/11', 'gradle', 'maven'],
            cannotCreateReason: hasCreatedIdeaU ? '您已创建带正版License的IDEA，仅可创建一个' : undefined,
        },
        {
            codeSpaceKind: 'androidStudio',
            displayName: 'Android Studio',
            description: '友好支持安卓开发',
            tags: ['java', 'Android SDK Command-Line Tools'],
        },
        {
            codeSpaceKind: 'goland',
            displayName: 'GoLand',
            description: '友好支持Go开发',
            tags: ['go'],
        },
        {
            codeSpaceKind: 'rust',
            displayName: 'Rust',
            description: '一门赋予每个人构建可靠且高效软件能力的语言',
            tags: ['rust'],
        },
        {
            codeSpaceKind: 'vscode',
            displayName: 'VS Code',
            description: '支持 C/C++、Go、JavaScript、PHP、Python 等多种语言',
            tags: ['c', 'c++', 'go', 'js', 'php', 'python'],
        },
    ];
};

export const operatingSystemDisplayNameMapping = {
    'centos-6u3': 'CentOS 6U3',
    'centos-7u5': 'CentOS 7U5',
    'ubuntu': 'Ubuntu',
};
